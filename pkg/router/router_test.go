package router

import (
	"api.appio.so/pkg/config"
	"api.appio.so/services"
	"fmt"
	"github.com/go-chi/chi/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestNewRouter(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Create minimal service container for testing
	serviceContainer := &services.ServiceContainer{
		APIKeyService:       &services.APIKeyService{},
		FeatureFlagService:  &services.FeatureFlagService{},
		ServiceService:      &services.ServiceService{},
		DeviceService:       &services.DeviceService{},
		NotificationService: &services.NotificationService{},
		WidgetService:       &services.WidgetService{},
		WidgetConfigService: &services.WidgetConfigService{},
		FingerprintService:  &services.FingerprintService{},
	}

	cfg := Config{
		Logger:            logger,
		DB:                &pgxpool.Pool{},
		DBFing:            &pgxpool.Pool{},
		Config:            &config.Config{
			Server: config.ServerConfig{
				Timeout: "30s",
				Env:     "test",
			},
		},
		Services:          serviceContainer,
		RateLimitRequests: 100,
		RateLimitWindow:   60,
		RateLimitBurst:    10,
	}

	t.Run("Creates router successfully", func(t *testing.T) {
		router := NewRouter(cfg)
		assert.NotNil(t, router)
		assert.IsType(t, &chi.Mux{}, router)
	})

	t.Run("Health check endpoint works", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Not found handler works", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/nonexistent-endpoint", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Most routes require authentication, so we expect 401 for protected routes
		// Only /health-check is public
		assert.True(t, w.Code == http.StatusNotFound || w.Code == http.StatusUnauthorized)
	})

	t.Run("Method not allowed handler works", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("POST", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Could be method not allowed or unauthorized depending on middleware order
		assert.True(t, w.Code == http.StatusMethodNotAllowed || w.Code == http.StatusUnauthorized)
	})
}

func TestRouterMiddlewares(t *testing.T) {
	logger := zaptest.NewLogger(t)

	serviceContainer := &services.ServiceContainer{
		APIKeyService:       &services.APIKeyService{},
		FeatureFlagService:  &services.FeatureFlagService{},
		ServiceService:      &services.ServiceService{},
		DeviceService:       &services.DeviceService{},
		NotificationService: &services.NotificationService{},
		WidgetService:       &services.WidgetService{},
		WidgetConfigService: &services.WidgetConfigService{},
		FingerprintService:  &services.FingerprintService{},
	}

	cfg := Config{
		Logger:            logger,
		DB:                &pgxpool.Pool{},
		DBFing:            &pgxpool.Pool{},
		Config:            &config.Config{
			Server: config.ServerConfig{
				Timeout: "30s",
				Env:     "test",
			},
		},
		Services:          serviceContainer,
		RateLimitRequests: 100,
		RateLimitWindow:   60,
		RateLimitBurst:    10,
	}

	t.Run("Content-Type header is set", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// The middleware sets the header, but the handler might override it
		contentType := w.Header().Get("Content-Type")
		assert.True(t, contentType == "application/json; charset=utf-8" || contentType == "application/json")
	})

	t.Run("X-Content-Type-Options header is set", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, "nosniff", w.Header().Get("X-Content-Type-Options"))
	})
}

func TestRouterConfiguration(t *testing.T) {
	logger := zaptest.NewLogger(t)

	serviceContainer := &services.ServiceContainer{
		APIKeyService:       &services.APIKeyService{},
		FeatureFlagService:  &services.FeatureFlagService{},
		ServiceService:      &services.ServiceService{},
		DeviceService:       &services.DeviceService{},
		NotificationService: &services.NotificationService{},
		WidgetService:       &services.WidgetService{},
		WidgetConfigService: &services.WidgetConfigService{},
		FingerprintService:  &services.FingerprintService{},
	}

	t.Run("Different rate limit configurations", func(t *testing.T) {
		testCases := []struct {
			name     string
			requests int
			window   int
			burst    int
		}{
			{"Low limits", 10, 60, 5},
			{"High limits", 1000, 3600, 100},
			{"Minimal limits", 1, 1, 1}, // Avoid zero division
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				cfg := Config{
					Logger:            logger,
					DB:                &pgxpool.Pool{},
					DBFing:            &pgxpool.Pool{},
					Config:            &config.Config{
						Server: config.ServerConfig{
							Timeout: "30s",
							Env:     "test",
						},
					},
					Services:          serviceContainer,
					RateLimitRequests: tc.requests,
					RateLimitWindow:   tc.window,
					RateLimitBurst:    tc.burst,
				}

				router := NewRouter(cfg)
				assert.NotNil(t, router)
			})
		}
	})

	t.Run("Different environments", func(t *testing.T) {
		environments := []string{"development", "staging", "production", "test"}

		for _, env := range environments {
			t.Run("Environment: "+env, func(t *testing.T) {
				cfg := Config{
					Logger: logger,
					DB:     &pgxpool.Pool{},
					DBFing: &pgxpool.Pool{},
					Config: &config.Config{
						Server: config.ServerConfig{
							Timeout: "30s",
							Env:     env,
						},
					},
					Services:          serviceContainer,
					RateLimitRequests: 100,
					RateLimitWindow:   60,
					RateLimitBurst:    10,
				}

				router := NewRouter(cfg)
				assert.NotNil(t, router)
			})
		}
	})

	t.Run("Different timeout configurations", func(t *testing.T) {
		timeouts := []int{10, 30, 60, 120}

		for _, timeout := range timeouts {
			t.Run("Timeout", func(t *testing.T) {
				cfg := Config{
					Logger: logger,
					DB:     &pgxpool.Pool{},
					DBFing: &pgxpool.Pool{},
					Config: &config.Config{
						Server: config.ServerConfig{
							Timeout: fmt.Sprintf("%ds", timeout),
							Env:     "test",
						},
					},
					Services:          serviceContainer,
					RateLimitRequests: 100,
					RateLimitWindow:   60,
					RateLimitBurst:    10,
				}

				router := NewRouter(cfg)
				assert.NotNil(t, router)
			})
		}
	})
}

func TestConfigStruct(t *testing.T) {
	t.Run("Config struct fields", func(t *testing.T) {
		cfg := Config{
			Logger:            zaptest.NewLogger(t),
			DB:                &pgxpool.Pool{},
			DBFing:            &pgxpool.Pool{},
			Config:            &config.Config{},
			Services:          &services.ServiceContainer{},
			RateLimitRequests: 100,
			RateLimitWindow:   60,
			RateLimitBurst:    10,
		}

		assert.NotNil(t, cfg.Logger)
		assert.NotNil(t, cfg.DB)
		assert.NotNil(t, cfg.DBFing)
		assert.NotNil(t, cfg.Config)
		assert.NotNil(t, cfg.Services)
		assert.Equal(t, 100, cfg.RateLimitRequests)
		assert.Equal(t, 60, cfg.RateLimitWindow)
		assert.Equal(t, 10, cfg.RateLimitBurst)
	})

	t.Run("Config with nil values", func(t *testing.T) {
		cfg := Config{
			Logger:            nil,
			DB:                nil,
			DBFing:            nil,
			Config:            nil,
			Services:          nil,
			RateLimitRequests: 0,
			RateLimitWindow:   0,
			RateLimitBurst:    0,
		}

		// Should panic when creating router with nil values
		// because the router tries to access config fields
		assert.Panics(t, func() {
			NewRouter(cfg)
		})
	})
}
