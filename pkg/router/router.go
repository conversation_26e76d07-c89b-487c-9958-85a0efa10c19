package router

import (
	"api.appio.so/handlers"
	"api.appio.so/middlewares"
	"api.appio.so/models"
	"api.appio.so/pkg/config"
	"api.appio.so/pkg/roles"
	"api.appio.so/repositories"
	"api.appio.so/services"
	"github.com/appio-so/go-appioid"
	"github.com/appio-so/go-clientip"
	"github.com/appio-so/go-zaplog"
	"github.com/go-chi/chi/v5"
	chimid "github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

type Config struct {
	Logger            *zap.Logger
	DB                *pgxpool.Pool
	DBFing            *pgxpool.Pool
	Config            *config.Config
	Services          *services.ServiceContainer
	RateLimitRequests int
	RateLimitWindow   int
	RateLimitBurst    int
}

func NewRouter(cfg Config) *chi.Mux {
	r := chi.NewRouter()
	r.Use(
		clientip.NewMiddleware().NewHandler,
		chimid.Recoverer,
		chimid.SetHeader("Content-Type", "application/json; charset=utf-8"),
		chimid.SetHeader("X-Content-Type-Options", "nosniff"),
		zaplog.NewMiddleware(cfg.Logger, zaplog.WithSentryScope(), zaplog.WithLogRequest(), zaplog.WithLogRequestBodyHeaders()).NewHandler,
		middlewares.TimeoutMiddleware(cfg.Config.Server.Timeout, cfg.Logger),
		middlewares.RateLimit(cfg.Logger, cfg.Config.Server.Env, cfg.RateLimitRequests, cfg.RateLimitWindow, cfg.RateLimitBurst),
	)

	// Public routes without auth
	r.Get("/", handlers.IndexHandler())
	r.Get("/robots.txt", handlers.RobotsTxtHandler())
	r.Get("/health-check", handlers.HealthCheckHandler())
	r.NotFound(handlers.NotFoundHandler())
	r.MethodNotAllowed(handlers.NotFoundHandler())

	featureFlagHandler := handlers.NewFeatureFlagHandler(cfg.Services.FeatureFlagService, cfg.Logger)
	serviceHandler := handlers.NewServiceHandler(cfg.Services.ServiceService, cfg.Logger)
	deviceHandler := handlers.NewDeviceHandler(cfg.Services.DeviceService, cfg.Logger)
	notificationHandler := handlers.NewNotificationHandler(cfg.Services.NotificationService, cfg.Logger)
	widgetHandler := handlers.NewWidgetHandler(cfg.Services.WidgetService, cfg.Services.WidgetConfigService, cfg.Logger)
	fingerprintHandler := handlers.NewFingerprintHandler(cfg.Services.FingerprintService, cfg.Logger)
	feedbackHandler := handlers.NewFeedbackHandler(cfg.Services.FeedbackService, cfg.Logger)

	// Create device repository for LastSeenAt middleware
	deviceRepository := repositories.NewDeviceRepository(cfg.DB)

	r.Route("/", func(r chi.Router) {
		r.Use(
			cors.AllowAll().Handler, // TODO: allow only some domains + localhost
			middlewares.AuthRoleService(cfg.Services.APIKeyService, cfg.Services.JWTService),
		)

		r.Route("/mobile", func(r chi.Router) {
			r.Use(
				middlewares.RoleMiddleware(cfg.Logger, roles.IOS, roles.Android),
				middlewares.HeaderToContextMiddleware("X-Service-Id", middlewares.SvcIDKey{}, appioid.Parse, cfg.Logger),
				middlewares.HeaderToContextMiddleware("X-Device-Id", middlewares.DvcIDKey{}, appioid.Parse, cfg.Logger),
				// NOTE: no service/device access validation.
				// TODO: delete default value once ios v1.2 is released. iOS app v1.1 did not send `platform` in Header
				middlewares.HeaderToContextMiddleware("X-App-Platform", middlewares.PlatformKey{}, models.ParsePlatform, cfg.Logger, models.PlatformIOS),
				middlewares.LastSeenAtMiddleware(deviceRepository, cfg.Logger),
			)

			r.Get("/ff", featureFlagHandler.Get)

			r.Post("/fingerprints/match", fingerprintHandler.Match)

			r.Get("/services", serviceHandler.ListByDeviceWithWidgetConfigs)
			r.Get("/services/{id}", serviceHandler.GetWithWidgetConfigs)

			r.Post("/devices", deviceHandler.CreateAndLink)
			r.Patch("/devices/{id}", deviceHandler.Update)
			r.Post("/devices/{id}/services", deviceHandler.LinkWithService)
			r.Delete("/devices/{id}", deviceHandler.Deactivate)

			r.Post("/feedback", feedbackHandler.Create)

			r.Get("/notifications", notificationHandler.ListDeliveredByDevice)

			r.Get("/widgets/{id}", widgetHandler.GetConfig)
		})

		r.Route("/demo-appio-so", func(r chi.Router) {
			r.Use(
				middlewares.RoleMiddleware(cfg.Logger, roles.DemoAppioSo),
				middlewares.HeaderToContextMiddleware("X-Service-Id", middlewares.SvcIDKey{}, appioid.Parse, cfg.Logger),
				middlewares.ValidateServiceIDPrefix("demo_svc", cfg.Logger),
			)

			r.Get("/services/{id}", serviceHandler.GetWithWidgets)
			r.Post("/services", serviceHandler.CreateDemo)
			r.Patch("/services/{id}", serviceHandler.Update)

			r.Get("/devices", deviceHandler.List)
			r.Post("/devices", deviceHandler.CreateAndLink)

			r.Get("/notifications", notificationHandler.List)
			r.Post("/notifications", notificationHandler.Create)

			r.Get("/widgets/{id}", widgetHandler.Get)
			r.Post("/widgets", widgetHandler.Create)
			r.Patch("/widgets/{id}", widgetHandler.Update)
		})

		r.Route("/app-appio-so", func(r chi.Router) {
			r.Use(
				middlewares.RoleMiddleware(cfg.Logger, roles.AppAppioSo),
				middlewares.HeaderToContextMiddleware("X-Service-Id", middlewares.SvcIDKey{}, appioid.Parse, cfg.Logger),
				// NOTE: no service access validation
			)

			r.Post("/fingerprints", fingerprintHandler.Create)
			r.Get("/services/{id}", serviceHandler.Get)
		})

		r.Route("/v1", func(r chi.Router) {
			r.Use(
				middlewares.RoleMiddleware(cfg.Logger, roles.Api, roles.ApiDemo, roles.Dashboard),
				// TESTING
				// TODO: extract organizationID from auth token
				middlewares.HeaderToContextMiddleware("X-Service-Id", middlewares.SvcIDKey{}, appioid.Parse, cfg.Logger),
				// TODO: if service set, validate that this user/organization owns this service.
				// some routes do not use x-service-id, like /services to list all services for a given organization
				//middlewares.ValidateServiceAccess(cfg.Services.APIKeyService, cfg.Logger),
			)

			r.Get("/services", serviceHandler.ListByOrganization)
			r.Get("/services/{id}", serviceHandler.Get)
			r.Patch("/services/{id}", serviceHandler.Update)

			r.Get("/devices", deviceHandler.List)
			r.Get("/devices/{id}", deviceHandler.Get)
			r.Delete("/devices", deviceHandler.DeactivateByUser)
			r.Delete("/devices/{id}", deviceHandler.Deactivate)

			r.Get("/notifications", notificationHandler.List)
			r.Get("/notifications/{id}", notificationHandler.Get)
			r.Post("/notifications", notificationHandler.Create)

			r.Get("/widgets", widgetHandler.List)
			r.Get("/widgets/{id}", widgetHandler.Get)
			r.Post("/widgets", widgetHandler.Create)
			r.Patch("/widgets/{id}", widgetHandler.Update)
			r.Delete("/widgets/{id}", widgetHandler.Delete)
		})

		// my.appio.so
		r.Route("/dashboard", func(r chi.Router) {
			r.Use(
				middlewares.RoleMiddleware(cfg.Logger, roles.Dashboard),
				// TODO: extract organizationID from auth token
			)

			r.Get("/services", serviceHandler.ListByOrganization)
		})

		r.With(middlewares.RoleMiddleware(cfg.Logger, roles.Api, roles.ApiDemo, roles.AppAppioSo, roles.DemoAppioSo, roles.IOS, roles.Android)).Get("/hi", handlers.HiHandler())
	})

	return r
}
