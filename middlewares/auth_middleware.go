package middlewares

import (
	"context"
	"net/http"
	"strings"

	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"api.appio.so/services"
)

func AuthRoleService(apiKeyService *services.APIKeyService, jwtService services.JWTServiceInterface) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			auth := r.Header.Get("Authorization")
			if auth == "" {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			token := strings.TrimPrefix(auth, "Bearer ")
			if token == "" {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			if !apiKeyService.IsValidApiKey(token) {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			ctx := r.Context()
			_, role, err := apiKeyService.GetServiceIDAndRoleByAPIKey(ctx, token)
			if err != nil {
				helpers.RenderJSONError(w, r, err)
				return
			}

			// TODO: TESTING disabled svcID from api_keys db
			//// Attach to the context
			//if svcID != nil {
			//	ctx = context.WithValue(ctx, SvcIDKey{}, svcID)
			//}
			ctx = context.WithValue(ctx, roleKey{}, role)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

/*
- try Authorization header
	- if valid (not empty and starts with "Bearer "), parse and return role and go to next handler
	- if invalid, continue
- try JWT header
	- if valid (jwt_service.IsValidJWT), parse return role and go to next handler
	- if invalid, continue
- end with outputting ErrUnauthorized

TODO: will need to parse SvcIDKey somewhere else. but should we at all? better not as API key could be linked to multiple services.

--------------------------------

auth := r.Header.Get("Authorization")
if auth == "" {
helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
return
}

token := strings.TrimPrefix(auth, "Bearer ")
if token == "" {
helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
return
}

ctx := r.Context()

var svcID *appioid.ID
var user *models.User
var role = roles.Unknown
var err error

if apiKeyService.IsValidApiKey(token) {
// TODO: should serviceId be coming from a different middleware?
svcID, role, err = apiKeyService.GetServiceIDAndRoleByAPIKey(ctx, token)
if err != nil {
helpers.RenderJSONError(w, r, err)
return
}
} else if jwtService.IsValidJWT(token) {
user, role, err = jwtService.ParseJWT(ctx, token)

// TODO: do something with User from JWT. store in context?
_ = user
}

// Attach to the context
if svcID != nil {
// TODO: should this be detected from API key or as part of Header?
ctx = context.WithValue(ctx, SvcIDKey{}, svcID)
}
ctx = context.WithValue(ctx, roleKey{}, role)

next.ServeHTTP(w, r.WithContext(ctx))
*/
