package middlewares

import (
	"api.appio.so/repositories"
	"go.uber.org/zap"
	"net/http"
)

// LastSeenAtMiddleware updates the device's last_seen_at field when a device ID is present in the context
func LastSeenAtMiddleware(deviceRepo repositories.DeviceRepositoryInterface, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			dvcID, ok := GetDeviceIDFromContext(ctx)
			if ok && dvcID != nil {
				// Don't fail the request if this update fails
				if err := deviceRepo.UpdateLastSeenAt(ctx, dvcID); err != nil {
					logger.Warn("failed to update device last_seen_at",
						zap.String("device_id", dvcID.String()),
						zap.Error(err))
				}
			}

			next.Serve<PERSON><PERSON>(w, r)
		})
	}
}
