package handlers

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHiHandler(t *testing.T) {
	t.Run("Returns hi response", func(t *testing.T) {
		handler := HiHandler()
		req := httptest.NewRequest("GET", "/hi", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "👋")
		assert.Equal(t, "application/json", w.<PERSON>er().Get("Content-Type"))
	})

	t.Run("Works with different HTTP methods", func(t *testing.T) {
		handler := HiHandler()
		methods := []string{"GET", "POST", "PUT", "DELETE", "PATCH"}

		for _, method := range methods {
			t.Run("Method: "+method, func(t *testing.T) {
				req := httptest.NewRequest(method, "/hi", nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusOK, w.Code)
				assert.Contains(t, w.Body.String(), "👋")
			})
		}
	})

	t.Run("Handler function is not nil", func(t *testing.T) {
		handler := HiHandler()
		assert.NotNil(t, handler)
	})

	t.Run("Returns http.HandlerFunc type", func(t *testing.T) {
		handler := HiHandler()
		assert.IsType(t, http.HandlerFunc(nil), handler)
	})

	t.Run("Sets Content-Type header correctly", func(t *testing.T) {
		handler := HiHandler()
		req := httptest.NewRequest("GET", "/hi", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		contentType := w.Header().Get("Content-Type")
		assert.Equal(t, "application/json", contentType)
	})

	t.Run("Response body is valid JSON", func(t *testing.T) {
		handler := HiHandler()
		req := httptest.NewRequest("GET", "/hi", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.NotEmpty(t, w.Body.String())
		// Should contain valid JSON response
		body := w.Body.String()
		assert.True(t, len(body) > 0)
	})

	t.Run("Consistent response across multiple calls", func(t *testing.T) {
		handler := HiHandler()
		
		var responses []string
		for i := 0; i < 3; i++ {
			req := httptest.NewRequest("GET", "/hi", nil)
			w := httptest.NewRecorder()

			handler(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			responses = append(responses, w.Body.String())
		}

		// All responses should be identical
		for i := 1; i < len(responses); i++ {
			assert.Equal(t, responses[0], responses[i])
		}
	})

	t.Run("Works with different URL paths", func(t *testing.T) {
		handler := HiHandler()
		paths := []string{"/hi", "/hello", "/greeting", "/"}

		for _, path := range paths {
			t.Run("Path: "+path, func(t *testing.T) {
				req := httptest.NewRequest("GET", path, nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusOK, w.Code)
				assert.Contains(t, w.Body.String(), "👋")
				assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
			})
		}
	})
}
