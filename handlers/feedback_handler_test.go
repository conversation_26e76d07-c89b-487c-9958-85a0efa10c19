package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/middlewares"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func setupFeedbackHandler(t *testing.T) (*FeedbackHandler, *mocks.MockFeedbackServiceInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockService := mocks.NewMockFeedbackServiceInterface(ctrl)
	logger := zaptest.NewLogger(t)
	handler := NewFeedbackHandler(mockService, logger)
	return handler, mockService, ctrl
}

func TestFeedbackHandler_Create(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success", func(t *testing.T) {
		handler, mockService, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		feedbackReq := models.FeedbackRequest{
			Message: "This is test feedback",
		}

		mockService.EXPECT().
			CreateFeedback(gomock.Any(), models.PlatformIOS, "1.2.3", net.ParseIP("127.0.0.1"), svcID, dvcID, "This is test feedback").
			Return(appioid.MustParse("fdb_00000000000000000000000001"), nil)

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-App-Version", "1.2.3")

		// Add service, device IDs and platform to context
		ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, middlewares.DvcIDKey{}, dvcID)
		ctx = context.WithValue(ctx, middlewares.PlatformKey{}, models.PlatformIOS)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
	})

	t.Run("InvalidJSON", func(t *testing.T) {
		handler, _, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")

		// Add service and device IDs to context
		ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, middlewares.DvcIDKey{}, dvcID)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("EmptyMessage", func(t *testing.T) {
		handler, _, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		feedbackReq := models.FeedbackRequest{
			Message: "",
		}

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Add service and device IDs to context
		ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, middlewares.DvcIDKey{}, dvcID)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("MessageTooLong", func(t *testing.T) {
		handler, _, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		// Create a message longer than 5000 characters
		longMessage := make([]byte, 5001)
		for i := range longMessage {
			longMessage[i] = 'a'
		}

		feedbackReq := models.FeedbackRequest{
			Message: string(longMessage),
		}

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Add service and device IDs to context
		ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, middlewares.DvcIDKey{}, dvcID)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("SuccessWithNullServiceID", func(t *testing.T) {
		handler, mockService, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		feedbackReq := models.FeedbackRequest{
			Message: "This is test feedback",
		}

		mockService.EXPECT().
			CreateFeedback(gomock.Any(), models.PlatformIOS, "1.2.3", net.ParseIP("127.0.0.1"), (*appioid.ID)(nil), dvcID, "This is test feedback").
			Return(appioid.MustParse("fdb_00000000000000000000000001"), nil)

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-App-Version", "1.2.3")

		// Add only device ID and platform to context, service ID is null
		ctx := context.WithValue(req.Context(), middlewares.DvcIDKey{}, dvcID)
		ctx = context.WithValue(ctx, middlewares.PlatformKey{}, models.PlatformIOS)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
	})

	t.Run("SuccessWithNullDeviceID", func(t *testing.T) {
		handler, mockService, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		feedbackReq := models.FeedbackRequest{
			Message: "This is test feedback",
		}

		mockService.EXPECT().
			CreateFeedback(gomock.Any(), models.PlatformIOS, "1.2.3", net.ParseIP("127.0.0.1"), svcID, (*appioid.ID)(nil), "This is test feedback").
			Return(appioid.MustParse("fdb_00000000000000000000000001"), nil)

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-App-Version", "1.2.3")

		// Add only service ID and platform to context, device ID is null
		ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, middlewares.PlatformKey{}, models.PlatformIOS)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
	})

	t.Run("SuccessWithBothIDsNull", func(t *testing.T) {
		handler, mockService, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		feedbackReq := models.FeedbackRequest{
			Message: "This is test feedback from intro screen",
		}

		mockService.EXPECT().
			CreateFeedback(gomock.Any(), models.PlatformIOS, "1.2.3", net.ParseIP("127.0.0.1"), (*appioid.ID)(nil), (*appioid.ID)(nil), "This is test feedback from intro screen").
			Return(appioid.MustParse("fdb_00000000000000000000000001"), nil)

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-App-Version", "1.2.3")

		// No service or device ID in context (intro screen scenario), but platform is needed
		ctx := context.WithValue(req.Context(), middlewares.PlatformKey{}, models.PlatformIOS)
		req = req.WithContext(ctx)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
	})

	t.Run("ServiceError", func(t *testing.T) {
		handler, mockService, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		feedbackReq := models.FeedbackRequest{
			Message: "This is test feedback",
		}

		mockService.EXPECT().
			CreateFeedback(gomock.Any(), models.PlatformIOS, "1.2.3", net.ParseIP("127.0.0.1"), svcID, dvcID, "This is test feedback").
			Return(nil, pkg.ErrInternal)

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-App-Version", "1.2.3")

		// Add service, device IDs and platform to context
		ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, middlewares.DvcIDKey{}, dvcID)
		ctx = context.WithValue(ctx, middlewares.PlatformKey{}, models.PlatformIOS)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("SuccessWithAndroidPlatform", func(t *testing.T) {
		handler, mockService, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		feedbackReq := models.FeedbackRequest{
			Message: "This is Android feedback",
		}

		mockService.EXPECT().
			CreateFeedback(gomock.Any(), models.PlatformAndroid, "1.2.3", net.ParseIP("127.0.0.1"), svcID, dvcID, "This is Android feedback").
			Return(appioid.MustParse("fdb_00000000000000000000000001"), nil)

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-App-Version", "1.2.3")

		// Add service, device IDs and platform to context
		ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, middlewares.DvcIDKey{}, dvcID)
		ctx = context.WithValue(ctx, middlewares.PlatformKey{}, models.PlatformAndroid)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
	})

	t.Run("MissingVersion", func(t *testing.T) {
		handler, _, ctrl := setupFeedbackHandler(t)
		defer ctrl.Finish()

		feedbackReq := models.FeedbackRequest{
			Message: "This is test feedback",
		}

		reqBody, _ := json.Marshal(feedbackReq)
		req := httptest.NewRequest("POST", "/feedback", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		// Missing X-App-Version header

		// Add service and device IDs to context
		ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, middlewares.DvcIDKey{}, dvcID)
		ctx = context.WithValue(ctx, middlewares.PlatformKey{}, models.PlatformIOS)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
