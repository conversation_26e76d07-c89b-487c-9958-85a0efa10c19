package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/models"

	"api.appio.so/internal/mocks"
	"api.appio.so/middlewares"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func setupNotificationHandler(t *testing.T) (*NotificationHandler, *mocks.MockNotificationServiceInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockService := mocks.NewMockNotificationServiceInterface(ctrl)
	logger := zaptest.NewLogger(t)
	handler := NewNotificationHandler(mockService, logger)
	return handler, mockService, ctrl
}

func addServiceIDToNotificationContext(req *http.Request, svcID *appioid.ID) *http.Request {
	ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
	return req.WithContext(ctx)
}

func addDeviceIDToNotificationContext(req *http.Request, dvcID *appioid.ID) *http.Request {
	ctx := context.WithValue(req.Context(), middlewares.DvcIDKey{}, dvcID)
	return req.WithContext(ctx)
}

func TestNewNotificationHandler(t *testing.T) {
	t.Run("Creates notification handler correctly", func(t *testing.T) {
		handler, _, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		assert.NotNil(t, handler)
		assert.NotNil(t, handler.service)
		assert.NotNil(t, handler.logger)
	})
}

func TestNotificationHandler_Get(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	ntfID := appioid.MustParse("ntf_00000000000000000000000001")

	t.Run("Success - notification found", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		expectedNotification := &models.NotificationResponseWithStats{
			ID: ntfID,
		}

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID, ntfID).
			Return(expectedNotification, nil)

		router := chi.NewRouter()
		router.Get("/notifications/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/notifications/"+ntfID.String(), nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.NotificationResponseWithStats
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, ntfID, response.ID)
	})

	t.Run("RawError - notification not found", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID, ntfID).
			Return(nil, nil)

		router := chi.NewRouter()
		router.Get("/notifications/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/notifications/"+ntfID.String(), nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID, ntfID).
			Return(nil, pkg.ErrInternal)

		router := chi.NewRouter()
		router.Get("/notifications/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/notifications/"+ntfID.String(), nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Get("/notifications/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/notifications/"+ntfID.String(), nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - invalid notification ID", func(t *testing.T) {
		handler, _, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Get("/notifications/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/notifications/invalid-id", nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestNotificationHandler_ListDeliveredByDevice(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success - notifications found", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		expectedNotifications := []models.NotificationResponse{
			{ID: appioid.MustParse("ntfdlv_00000000000000000000000001")},
			{ID: appioid.MustParse("ntfdlv_00000000000000000000000002")},
		}

		mockService.EXPECT().
			ListDeliveredByDevice(gomock.Any(), svcID, dvcID).
			Return(expectedNotifications, nil)

		req := httptest.NewRequest("GET", "/notifications/delivered", nil)
		req = addServiceIDToNotificationContext(req, svcID)
		req = addDeviceIDToNotificationContext(req, dvcID)
		w := httptest.NewRecorder()

		handler.ListDeliveredByDevice(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response []models.NotificationResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response, 2)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			ListDeliveredByDevice(gomock.Any(), svcID, dvcID).
			Return(nil, pkg.ErrInternal)

		req := httptest.NewRequest("GET", "/notifications/delivered", nil)
		req = addServiceIDToNotificationContext(req, svcID)
		req = addDeviceIDToNotificationContext(req, dvcID)
		w := httptest.NewRecorder()

		handler.ListDeliveredByDevice(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("GET", "/notifications/delivered", nil)
		req = addDeviceIDToNotificationContext(req, dvcID)
		w := httptest.NewRecorder()

		handler.ListDeliveredByDevice(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - missing device ID", func(t *testing.T) {
		handler, _, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("GET", "/notifications/delivered", nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.ListDeliveredByDevice(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestNotificationHandler_List(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success - list all notifications", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		expectedNotifications := []models.NotificationResponse{
			{ID: appioid.MustParse("ntf_00000000000000000000000001")},
		}

		mockService.EXPECT().
			List(gomock.Any(), svcID, "").
			Return(expectedNotifications, nil)

		req := httptest.NewRequest("GET", "/notifications", nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response []models.NotificationResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response, 1)
	})

	t.Run("Success - list by device ID", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		expectedNotifications := []models.NotificationResponse{
			{ID: appioid.MustParse("ntfdlv_00000000000000000000000001")},
		}

		mockService.EXPECT().
			ListAllByDevice(gomock.Any(), svcID, dvcID, "created").
			Return(expectedNotifications, nil)

		req := httptest.NewRequest("GET", "/notifications?device_id="+dvcID.String()+"&status=created", nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response []models.NotificationResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response, 1)
	})

	t.Run("Success - list by customer user ID", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		expectedNotifications := []models.NotificationResponse{
			{ID: appioid.MustParse("ntfdlv_00000000000000000000000001")},
		}

		mockService.EXPECT().
			ListAllByCustomerUserID(gomock.Any(), svcID, "user123", "delivered").
			Return(expectedNotifications, nil)

		req := httptest.NewRequest("GET", "/notifications?user_id=user123&status=delivered", nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response []models.NotificationResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response, 1)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			List(gomock.Any(), svcID, "").
			Return(nil, pkg.ErrInternal)

		req := httptest.NewRequest("GET", "/notifications", nil)
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("GET", "/notifications", nil)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestNotificationHandler_Create(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")
	ntfID := appioid.MustParse("ntf_00000000000000000000000001")

	t.Run("Success - notification created", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		ntfReq := models.NotificationRequest{
			Payload: json.RawMessage(`{"title": "Test Notification", "body": "Test Body"}`),
		}

		mockService.EXPECT().
			CreateForeground(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(ntfID, nil)

		reqBody, _ := json.Marshal(ntfReq)
		req := httptest.NewRequest("POST", "/notifications", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, ntfID, response.ID)
	})

	t.Run("Success - notification created with device filter", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		ntfReq := models.NotificationRequest{
			Payload: json.RawMessage(`{"title": "Test Notification", "body": "Test Body"}`),
		}

		mockService.EXPECT().
			CreateForeground(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(ntfID, nil)

		reqBody, _ := json.Marshal(ntfReq)
		req := httptest.NewRequest("POST", "/notifications?device_id="+dvcID.String(), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, ntfID, response.ID)
	})

	t.Run("Success - notification created with user filter", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		ntfReq := models.NotificationRequest{
			Payload: json.RawMessage(`{"title": "Test Notification", "body": "Test Body"}`),
		}

		mockService.EXPECT().
			CreateForeground(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(ntfID, nil)

		reqBody, _ := json.Marshal(ntfReq)
		req := httptest.NewRequest("POST", "/notifications?user_id=user123", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, ntfID, response.ID)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("POST", "/notifications", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		ntfReq := models.NotificationRequest{
			Payload: json.RawMessage(`{"title": "Test Notification", "body": "Test Body"}`),
		}

		mockService.EXPECT().
			CreateForeground(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return((*appioid.ID)(nil), pkg.ErrInternal)

		reqBody, _ := json.Marshal(ntfReq)
		req := httptest.NewRequest("POST", "/notifications", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToNotificationContext(req, svcID)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupNotificationHandler(t)
		defer ctrl.Finish()

		ntfReq := models.NotificationRequest{
			Payload: json.RawMessage(`{"title": "Test Notification", "body": "Test Body"}`),
		}

		reqBody, _ := json.Marshal(ntfReq)
		req := httptest.NewRequest("POST", "/notifications", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
