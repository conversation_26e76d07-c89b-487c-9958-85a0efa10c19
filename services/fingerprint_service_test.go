package services

import (
	"api.appio.so/models"
	"context"
	"errors"
	"net"
	"testing"
	"time"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap"
)

// --- Unit tests for FingerprintService ---

func TestFingerprintService_Match(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()

	// A sample request; add fields as required by your models.
	matchReq := models.FingerprintMatchRequest{
		IP: net.ParseIP("127.0.0.1"),
		FingerprintData: models.FingerprintData{
			UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
			ScreenResolution: "1792x1120",
			Language:         "en-GB",
			TimeOffset:       0,
		},
	}

	t.Run("Repository error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockFingerprintRepositoryInterface(ctrl)
		service := NewFingerprintService(repo, logger, true)

		repo.EXPECT().
			ListBy(ctx, matchReq).
			Return(nil, errors.New("db error"))

		fp, err := service.Match(ctx, models.PlatformIOS, matchReq)
		assert.Nil(t, fp)
		assert.Equal(t, pkg.ErrInternal, err)
	})

	t.Run("No fingerprints found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockFingerprintRepositoryInterface(ctrl)
		service := NewFingerprintService(repo, logger, false)

		repo.EXPECT().
			ListBy(ctx, matchReq).
			Return([]models.Fingerprint{}, nil)

		fp, err := service.Match(ctx, models.PlatformIOS, matchReq)
		assert.Nil(t, fp)
		assert.Equal(t, pkg.ErrNotFound, err)
	})

	t.Run("Multiple fingerprints found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockFingerprintRepositoryInterface(ctrl)
		service := NewFingerprintService(repo, logger, false)

		id1, _ := appioid.New("fp_00000000000000000000000001")
		id2, _ := appioid.New("fp_00000000000000000000000002")
		fingerprints := []models.Fingerprint{
			{ID: id1},
			{ID: id2},
		}
		repo.EXPECT().
			ListBy(ctx, matchReq).
			Return(fingerprints, nil)

		fp, err := service.Match(ctx, models.PlatformIOS, matchReq)
		assert.Nil(t, fp)
		assert.Equal(t, pkg.ErrMatch, err)
	})

	t.Run("One fingerprint found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockFingerprintRepositoryInterface(ctrl)
		service := NewFingerprintService(repo, logger, false)

		svcID, _ := appioid.New("svc_00000000000000000000000001")
		expectedID, _ := appioid.New("fp_00000000000000000000000001")
		expectedFingerprint := models.Fingerprint{
			ID:        expectedID,
			CreatedAt: time.Now(),
			FingerprintCreateRequest: models.FingerprintCreateRequest{
				IP:             net.ParseIP("127.0.0.1"),
				ServiceID:      svcID,
				CustomerUserID: "user12345",
				Data: models.FingerprintData{
					UserAgent:        "Chrome",
					ScreenResolution: "1792x1120",
					Language:         "en-GB",
					TimeOffset:       0,
				},
			},
			FingerprintData: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}
		repo.EXPECT().
			ListBy(ctx, matchReq).
			Return([]models.Fingerprint{expectedFingerprint}, nil)

		fp, err := service.Match(ctx, models.PlatformIOS, matchReq)
		assert.NoError(t, err)
		assert.NotNil(t, fp)
		assert.Equal(t, expectedFingerprint, *fp)
	})
}

func TestFingerprintService_Create(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()

	// In this example, we include a UserAgent that will be normalized.
	uaInput := "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Mobile/15E148 Safari/604.1"
	// Expected normalized UA (see comments in your source):
	expectedUA := "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148"
	createReq := models.FingerprintCreateRequest{
		Data: models.FingerprintData{
			UserAgent:        uaInput,
			ScreenResolution: "1792x1120",
			Language:         "en-GB",
			TimeOffset:       0,
		},
	}

	t.Run("Create success", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockFingerprintRepositoryInterface(ctrl)
		service := NewFingerprintService(repo, logger, false)

		// We expect that repository.Create is called with a request that has a normalized user agent.
		repo.EXPECT().
			Create(ctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, id *appioid.ID, req models.FingerprintCreateRequest) error {
				assert.Equal(t, expectedUA, req.Data.UserAgent)
				return nil
			})

		fingID, err := service.Create(ctx, createReq)
		assert.NoError(t, err)
		assert.NotNil(t, fingID)
	})

	t.Run("Create failure", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockFingerprintRepositoryInterface(ctrl)
		service := NewFingerprintService(repo, logger, false)

		repo.EXPECT().
			Create(ctx, gomock.Any(), gomock.Any()).
			Return(errors.New("create error"))

		fingID, err := service.Create(ctx, createReq)
		assert.Nil(t, fingID)
		assert.Equal(t, pkg.ErrInternal, err)
	})
}


