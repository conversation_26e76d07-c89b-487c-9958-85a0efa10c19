package services

import (
	"api.appio.so/models"
	"context"
	"regexp"
	"strings"

	"api.appio.so/pkg"
	"api.appio.so/repositories"
	"github.com/appio-so/go-appioid"
	"go.uber.org/zap"
)

// FingerprintServiceInterface defines the interface for fingerprint service operations
type FingerprintServiceInterface interface {
	Match(ctx context.Context, platform models.Platform, matchReq models.FingerprintMatchRequest) (*models.Fingerprint, error)
	Create(ctx context.Context, createReq models.FingerprintCreateRequest) (*appioid.ID, error)
}

type FingerprintService struct {
	repository  repositories.FingerprintRepositoryInterface
	logger      *zap.Logger
	logRequests bool
}

func NewFingerprintService(repository repositories.FingerprintRepositoryInterface, logger *zap.Logger, logRequests bool) *FingerprintService {
	return &FingerprintService{
		repository:  repository,
		logger:      logger,
		logRequests: logRequests,
	}
}

func (s *FingerprintService) Match(ctx context.Context, platform models.Platform, matchReq models.FingerprintMatchRequest) (*models.Fingerprint, error) {
	// TODO: consider platform in fingerprint matching
	// TODO: for partial match, return pkg.ErrPartialMatch witch should create response with http code 202,
	//       and instruct the app to request validation code.

	if s.logRequests {
		// N2H: log fingerprint match requests to a database?
		s.logger.Info("fingerprint match request", zap.Any("request", matchReq))
	}

	fingerprints, err := s.repository.ListBy(ctx, matchReq)
	if err != nil {
		s.logger.Error("finding fingerprints", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	if len(fingerprints) == 0 {
		s.logger.Debug("fingerprint not found")
		return nil, pkg.ErrNotFound
	}
	if len(fingerprints) > 1 {
		s.logger.Warn("multiple fingerprints found", zap.Any("fingerprints", fingerprints))
		return nil, pkg.ErrMatch
	}

	return &fingerprints[0], nil
}

func (s *FingerprintService) Create(ctx context.Context, freq models.FingerprintCreateRequest) (*appioid.ID, error) {
	fingID, err := appioid.New(models.FingerprintPrefix)
	if err != nil {
		return nil, pkg.ErrInternal
	}

	freq.Data.UserAgent = normaliseUserAgent(freq.Data.UserAgent)

	err = s.repository.Create(ctx, fingID, freq)
	if err != nil {
		s.logger.Error("creating fingerprint", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return fingID, nil
}

// --------------------------------------------------------------------------------------------------------------------

/*
APP: Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Mobile/15E148 Safari/604.1
SAF: Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
*/
func normaliseUserAgent(ua string) string {
	re := regexp.MustCompile(`\s*Version\/[0-9.]+|\s*Safari\/[0-9.]+`)
	return strings.TrimSpace(re.ReplaceAllString(ua, ""))
}
