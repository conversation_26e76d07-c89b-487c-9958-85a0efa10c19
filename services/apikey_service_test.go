package services

import (
	"api.appio.so/pkg/roles"
	"context"
	"strings"
	"sync"
	"testing"
	"time"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"api.appio.so/pkg/config"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap"
)

func TestAPIKeyService_GetServiceIDAndRoleByAPIKey(t *testing.T) {
	logger := zap.NewNop()

	// Mock AuthConfig object for the service
	authConfig := &config.AuthConfig{
		App:     "app-key_12345678901234567890123456789012345678901234567890",
		IOS:     "ios-key_12345678901234567890123456789012345678901234567890",
		Android: "android-key_12345678901234567890123456789012345678901234567890",
		Demo:    "demo-key_12345678901234567890123456789012345678901234567890",
	}

	// Test cases
	tests := []struct {
		name          string
		apiKey        string
		mockResponse  *appioid.ID
		mockError     error
		expectedID    *appioid.ID
		expectedRole  roles.Role
		expectedError error
	}{
		{
			name:          "Valid API Key",
			apiKey:        "api-key_12345678901234567890123456789012345678901234567890",
			mockResponse:  appioid.MustParse("svc_0123456789aaaaaaaaaaaaaaaa"),
			mockError:     nil,
			expectedID:    appioid.MustParse("svc_0123456789aaaaaaaaaaaaaaaa"),
			expectedRole:  roles.Api,
			expectedError: nil,
		},
		{
			name:          "App Key",
			apiKey:        "app-key_12345678901234567890123456789012345678901234567890",
			mockResponse:  nil,
			mockError:     nil,
			expectedID:    nil,
			expectedRole:  roles.AppAppioSo,
			expectedError: nil,
		},
		{
			name:          "iOS Key",
			apiKey:        "ios-key_12345678901234567890123456789012345678901234567890",
			mockResponse:  nil,
			mockError:     nil,
			expectedID:    nil,
			expectedRole:  roles.IOS,
			expectedError: nil,
		},
		{
			name:          "Demo Key",
			apiKey:        "demo-key_12345678901234567890123456789012345678901234567890",
			mockResponse:  nil,
			mockError:     nil,
			expectedID:    nil,
			expectedRole:  roles.DemoAppioSo,
			expectedError: nil,
		},
		{
			name:          "Valid Demo API Key",
			apiKey:        "demo_svc_00000000000000000000000000000000000000000",
			mockResponse:  nil, // Not used for demo API keys
			mockError:     nil,
			expectedID:    appioid.MustParse("demo_svc_00000000000000000000000000"),
			expectedRole:  roles.ApiDemo,
			expectedError: nil,
		},
		{
			name:          "Invalid Demo API Key - Invalid ID",
			apiKey:        "demo_some_prefix_invalid_id_format_!!!!!!!!!!!!!!!",
			mockResponse:  nil,
			mockError:     nil,
			expectedID:    nil,
			expectedRole:  roles.Unknown,
			expectedError: pkg.ErrInvalidInput,
		},
		{
			name:          "Invalid API Key",
			apiKey:        "invalid-key_00000000000000000000000000000000000000",
			mockResponse:  nil,
			mockError:     pgx.ErrNoRows,
			expectedID:    nil,
			expectedRole:  roles.Unknown,
			expectedError: nil, // Service handles pgx.ErrNoRows and doesn't propagate it
		},
		{
			name:          "RawError from repository",
			apiKey:        "error-key_0000000000000000000000000000000000000000",
			mockResponse:  nil,
			mockError:     context.DeadlineExceeded,
			expectedID:    nil,
			expectedRole:  roles.Unknown,
			expectedError: pkg.ErrInternal,
		},
		{
			name:          "Repository returns nil service ID",
			apiKey:        "nil-svc-key_000000000000000000000000000000000000000",
			mockResponse:  nil, // Repository returns nil service ID
			mockError:     nil,
			expectedID:    nil,
			expectedRole:  roles.Unknown,
			expectedError: nil, // Service handles nil gracefully
		},
	}

	// Run each test case
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Mock repository
			mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)

			// Only set up expectation if the mock should be called
			// (App, iOS, Demo keys don't call the repository)
			if tt.mockResponse != nil || tt.mockError != nil || strings.Contains(tt.apiKey, "nil-svc-key") {
				mockRepo.EXPECT().
					GetServiceIDByAPIKey(gomock.Any(), tt.apiKey).
					Return(tt.mockResponse, tt.mockError)
			}

			// Create the service with the mockRepo
			service := NewAPIKeyService(mockRepo, authConfig, logger)

			// Call the method being tested
			serviceID, role, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), tt.apiKey)

			// Validate the results
			if tt.expectedError != nil {
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, serviceID)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, serviceID)
				assert.Equal(t, tt.expectedRole, role)
			}
		})
	}
}

func TestAPIKeyService_RateLimiting(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zap.NewNop()
	authConfig := &config.AuthConfig{
		App:  "app-key",
		IOS:  "ios-key",
		Demo: "demo-key",
	}

	// Mock repository that always returns a database error (not pgx.ErrNoRows)
	// to trigger failed attempts for rate limiting
	mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
	testKey := "test-rate-limit-key_000000000000000000000000000000"

	// Expect 5 calls that will fail, then 1 call that should be blocked
	mockRepo.EXPECT().
		GetServiceIDByAPIKey(gomock.Any(), testKey).
		Return(nil, context.DeadlineExceeded).
		Times(5)

	service := NewAPIKeyService(mockRepo, authConfig, logger)

	// Make 4 failed attempts (should not be blocked yet)
	for i := 0; i < 4; i++ {
		serviceID, role, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
		assert.Nil(t, serviceID)
		assert.Equal(t, roles.Unknown, role)
		assert.Equal(t, pkg.ErrInternal, err) // Database errors propagate as ErrInternal
	}

	// 5th attempt should still work but will trigger blocking
	serviceID, role, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
	assert.Nil(t, serviceID)
	assert.Equal(t, roles.Unknown, role)
	assert.Equal(t, pkg.ErrInternal, err)

	// 6th attempt should be blocked
	serviceID, role, err = service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
	assert.Nil(t, serviceID)
	assert.Equal(t, roles.Unknown, role)
	assert.Equal(t, pkg.ErrTooManyAttempts, err)
}

func TestAPIKeyService_RateLimitingReset(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zap.NewNop()
	authConfig := &config.AuthConfig{
		App:  "app-key",
		IOS:  "ios-key",
		Demo: "demo-key",
	}

	testKey := "test-reset-key_00000000000000000000000000000000000"

	// Mock repository that fails first few times with real errors, then succeeds
	mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)

	// First 3 calls fail
	mockRepo.EXPECT().
		GetServiceIDByAPIKey(gomock.Any(), testKey).
		Return(nil, context.DeadlineExceeded).
		Times(3)

	// 4th and 5th calls succeed
	mockRepo.EXPECT().
		GetServiceIDByAPIKey(gomock.Any(), testKey).
		Return(appioid.MustParse("svc_0123456789aaaaaaaaaaaaaaaa"), nil).
		Times(2)

	service := NewAPIKeyService(mockRepo, authConfig, logger)

	// Make 3 failed attempts
	for i := 0; i < 3; i++ {
		serviceID, role, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
		assert.Nil(t, serviceID)
		assert.Equal(t, roles.Unknown, role)
		assert.Equal(t, pkg.ErrInternal, err) // Real errors propagate
	}

	// 4th attempt should succeed and reset the rate limiting
	serviceID, role, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
	assert.NotNil(t, serviceID)
	assert.Equal(t, roles.Api, role)
	assert.NoError(t, err)

	// Now the key should work normally again (no rate limiting)
	serviceID, role, err = service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
	assert.NotNil(t, serviceID)
	assert.Equal(t, roles.Api, role)
	assert.NoError(t, err)
}

func TestAPIKeyService_DemoAPIKeyEdgeCases(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zap.NewNop()
	authConfig := &config.AuthConfig{
		App:  "app-key",
		IOS:  "ios-key",
		Demo: "demo-key",
	}

	// Mock repository (not used for demo API keys, so no expectations needed)
	mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)

	service := NewAPIKeyService(mockRepo, authConfig, logger)

	tests := []struct {
		name          string
		apiKey        string
		expectedID    *appioid.ID
		expectedRole  roles.Role
		expectedError error
	}{
		{
			name:          "Valid Demo API Key - Minimum Length",
			apiKey:        "demo_12345_000000000000000000000000000000000000000",
			expectedID:    appioid.MustParse("demo_svc_00000000000000000000000000"),
			expectedRole:  roles.ApiDemo,
			expectedError: nil,
		},
		{
			name:          "Valid Demo API Key - Longer Prefix",
			apiKey:        "demo_very_long_prefix_here_00000000000000000000000000",
			expectedID:    appioid.MustParse("demo_svc_00000000000000000000000000"),
			expectedRole:  roles.ApiDemo,
			expectedError: nil,
		},
		{
			name:          "Invalid Demo API Key - Exactly 50 chars but invalid ID",
			apiKey:        "demo_prefix_invalid_id_format!!!!!!!!!!!!!!!!!!!!!",
			expectedID:    nil,
			expectedRole:  roles.Unknown,
			expectedError: pkg.ErrInvalidInput,
		},
		{
			name:          "Invalid Demo API Key - too short",
			apiKey:        "demo_prefix_00000000000000000", // Actually 30 chars
			expectedID:    nil,
			expectedRole:  roles.Unknown,
			expectedError: pkg.ErrInvalidInput,
		},
		{
			name:          "Demo prefix but not demo API key",
			apiKey:        "demo_short",
			expectedID:    nil,
			expectedRole:  roles.Unknown,
			expectedError: pkg.ErrInvalidInput,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			serviceID, role, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), tt.apiKey)

			if tt.expectedError != nil {
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, serviceID)
				assert.Equal(t, tt.expectedRole, role)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, serviceID)
				assert.Equal(t, tt.expectedRole, role)
			}
		})
	}
}

func TestAPIKeyService_ConfigurableParameters(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zap.NewNop()
	authConfig := &config.AuthConfig{
		App:  "app-key",
		IOS:  "ios-key",
		Demo: "demo-key",
	}

	// Test with custom configuration
	customConfig := APIKeyServiceConfig{
		MaxFailures:      3, // Lower threshold
		MaxBlockDuration: 1 * time.Hour,
		MinKeyLength:     30,  // Lower minimum
		MaxKeyLength:     100, // Lower maximum
		CleanupInterval:  5 * time.Minute,
		AttemptRetention: 12 * time.Hour,
	}

	mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
	testKey := "test_key_12345678901234567890123456789012345678901234567890"

	// Expect 3 calls that will fail for the custom failure threshold test
	mockRepo.EXPECT().
		GetServiceIDByAPIKey(gomock.Any(), testKey).
		Return(nil, context.DeadlineExceeded).
		Times(3)

	service := NewAPIKeyServiceWithConfig(mockRepo, authConfig, logger, customConfig)

	// Test key length validation with custom limits
	t.Run("Key too short", func(t *testing.T) {
		shortKey := "short_key_1234567890123456789" // 29 chars, below custom minimum of 30
		_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), shortKey)
		assert.Equal(t, pkg.ErrInvalidInput, err)
	})

	t.Run("Key too long", func(t *testing.T) {
		longKey := strings.Repeat("a", 101) // 101 chars, above custom maximum of 100
		_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), longKey)
		assert.Equal(t, pkg.ErrInvalidInput, err)
	})

	t.Run("Custom failure threshold", func(t *testing.T) {
		// Make 2 failed attempts (should not be blocked yet with threshold of 3)
		for i := 0; i < 2; i++ {
			_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
			assert.Equal(t, pkg.ErrInternal, err)
		}

		// 3rd attempt should still work but will trigger blocking
		_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
		assert.Equal(t, pkg.ErrInternal, err)

		// 4th attempt should be blocked
		_, _, err = service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
		assert.Equal(t, pkg.ErrTooManyAttempts, err)
	})
}

func TestAPIKeyService_LongDemoAPIKeys(t *testing.T) {
	logger := zap.NewNop()
	authConfig := &config.AuthConfig{
		App:  "app-key",
		IOS:  "ios-key",
		Demo: "demo-key",
	}

	// Use default config which allows up to 200 character keys
	service := NewAPIKeyService(nil, authConfig, logger)

	tests := []struct {
		name        string
		apiKey      string
		expectError bool
	}{
		{
			name:        "Long demo API key - should work",
			apiKey:      "demo_very_long_prefix_with_lots_of_text_here_00000000000000000000000000", // ~70 chars
			expectError: false,
		},
		{
			name:        "Very long demo API key - should work",
			apiKey:      "demo_extremely_long_prefix_with_even_more_text_and_numbers_12345_00000000000000000000000000", // ~90 chars
			expectError: false,
		},
		{
			name:        "Extremely long demo API key - should be rejected",
			apiKey:      strings.Repeat("demo_", 40) + "00000000000000000000000000", // >200 chars
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			serviceID, role, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), tt.apiKey)

			if tt.expectError {
				assert.Equal(t, pkg.ErrInvalidInput, err)
				assert.Nil(t, serviceID)
				assert.Equal(t, roles.Unknown, role)
			} else {
				// Should not fail due to length validation
				// (might fail for other reasons like invalid demo key format, but not length)
				if err == pkg.ErrInvalidInput {
					t.Errorf("Key should not be rejected due to length: %s (len=%d)", tt.apiKey, len(tt.apiKey))
				}
			}
		})
	}
}

func TestAPIKeyService_FixedBlockDuration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zap.NewNop()
	authConfig := &config.AuthConfig{
		App:  "app-key",
		IOS:  "ios-key",
		Demo: "demo-key",
	}

	// Test with custom block duration
	apiConfig := APIKeyServiceConfig{
		MaxFailures:      3,
		MaxBlockDuration: 30 * time.Minute, // Fixed 30-minute block
		MinKeyLength:     50,
		MaxKeyLength:     200,
		CleanupInterval:  10 * time.Minute,
		AttemptRetention: 24 * time.Hour,
	}

	mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
	testKey := "test_block_duration_12345678901234567890123456789012345678901234567890"

	// Expect 3 calls that will fail, then additional calls should be blocked
	mockRepo.EXPECT().
		GetServiceIDByAPIKey(gomock.Any(), testKey).
		Return(nil, context.DeadlineExceeded).
		Times(3)

	service := NewAPIKeyServiceWithConfig(mockRepo, authConfig, logger, apiConfig)

	// Make enough failed attempts to trigger blocking
	for i := 0; i < 3; i++ {
		_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
		assert.Equal(t, pkg.ErrInternal, err)
	}

	// Should be blocked now
	_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
	assert.Equal(t, pkg.ErrTooManyAttempts, err)

	// Verify the block duration is fixed regardless of attempt count
	// (We can't easily test time passage in unit tests, but we can verify the logic)

	// Make more failed attempts while blocked - should still be blocked
	for i := 0; i < 10; i++ {
		_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
		assert.Equal(t, pkg.ErrTooManyAttempts, err, "Should remain blocked regardless of additional attempts")
	}
}

func TestAPIKeyService_CleanupOldAttempts(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zap.NewNop()
	authConfig := &config.AuthConfig{
		App:  "app-key",
		IOS:  "ios-key",
		Demo: "demo-key",
	}

	// Test with custom configuration for faster cleanup testing
	customConfig := APIKeyServiceConfig{
		MaxFailures:      5,
		MaxBlockDuration: 1 * time.Hour,
		MinKeyLength:     50,
		MaxKeyLength:     200,
		CleanupInterval:  100 * time.Millisecond, // Very short for testing
		AttemptRetention: 200 * time.Millisecond, // Very short for testing
	}

	mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
	testKey := "cleanup-test-key_0000000000000000000000000000000000000000"

	// Expect calls for failed attempts
	mockRepo.EXPECT().
		GetServiceIDByAPIKey(gomock.Any(), testKey).
		Return(nil, context.DeadlineExceeded).
		Times(2)

	service := NewAPIKeyServiceWithConfig(mockRepo, authConfig, logger, customConfig)

	// Make some failed attempts to create entries in the attempts map
	for i := 0; i < 2; i++ {
		_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
		assert.Equal(t, pkg.ErrInternal, err)
	}

	// Verify the attempt is recorded
	service.attemptsMu.RLock()
	attempt, exists := service.attempts[testKey]
	service.attemptsMu.RUnlock()
	assert.True(t, exists, "Attempt should be recorded")
	assert.Equal(t, 2, attempt.Attempts, "Should have 2 failed attempts")

	// Wait for cleanup to occur (retention period + some buffer)
	time.Sleep(300 * time.Millisecond)

	// Verify the old attempt was cleaned up
	service.attemptsMu.RLock()
	_, exists = service.attempts[testKey]
	service.attemptsMu.RUnlock()
	assert.False(t, exists, "Old attempt should be cleaned up")
}

func TestAPIKeyService_ConcurrentAccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zap.NewNop()
	authConfig := &config.AuthConfig{
		App:  "app-key",
		IOS:  "ios-key",
		Demo: "demo-key",
	}

	// Test with custom configuration for predictable behavior
	customConfig := APIKeyServiceConfig{
		MaxFailures:      3,
		MaxBlockDuration: 1 * time.Hour,
		MinKeyLength:     50,
		MaxKeyLength:     200,
		CleanupInterval:  10 * time.Minute,
		AttemptRetention: 24 * time.Hour,
	}

	mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
	testKey := "concurrent-test-key_000000000000000000000000000000000000"

	// Expect multiple concurrent calls that will fail
	// Use AnyTimes() to handle the unpredictable nature of concurrent access
	mockRepo.EXPECT().
		GetServiceIDByAPIKey(gomock.Any(), testKey).
		Return(nil, context.DeadlineExceeded).
		AnyTimes()

	service := NewAPIKeyServiceWithConfig(mockRepo, authConfig, logger, customConfig)

	// Test concurrent access to rate limiting
	const numGoroutines = 10
	const callsPerGoroutine = 2

	var wg sync.WaitGroup
	results := make(chan error, numGoroutines*callsPerGoroutine)

	// Launch multiple goroutines making concurrent calls
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < callsPerGoroutine; j++ {
				_, _, err := service.GetServiceIDAndRoleByAPIKey(context.Background(), testKey)
				results <- err
			}
		}()
	}

	wg.Wait()
	close(results)

	// Collect results
	var internalErrors, tooManyAttemptsErrors int
	for err := range results {
		switch err {
		case pkg.ErrInternal:
			internalErrors++
		case pkg.ErrTooManyAttempts:
			tooManyAttemptsErrors++
		default:
			t.Errorf("Unexpected error: %v", err)
		}
	}

	// Verify that we got some internal errors (failed attempts) and some rate limiting errors
	assert.Greater(t, internalErrors, 0, "Should have some internal errors from failed attempts")

	// After enough concurrent failures, some should be blocked
	// The exact number depends on timing, but we should see some rate limiting
	if internalErrors >= customConfig.MaxFailures {
		assert.Greater(t, tooManyAttemptsErrors, 0, "Should have some rate limiting errors after enough failures")
	}

	// Verify the service state is consistent after concurrent access
	service.attemptsMu.RLock()
	attempt, exists := service.attempts[testKey]
	service.attemptsMu.RUnlock()

	if exists {
		assert.LessOrEqual(t, attempt.Attempts, internalErrors, "Recorded attempts should not exceed actual failed calls")
		if attempt.Attempts >= customConfig.MaxFailures {
			assert.NotNil(t, attempt.BlockedAt, "Should be blocked after max failures")
		}
	}
}
