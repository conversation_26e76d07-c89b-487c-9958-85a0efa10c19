package services

import (
	"time"

	"api.appio.so/pkg/config"
	"api.appio.so/repositories"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// ServiceContainer manages all application services and their lifecycles
type ServiceContainer struct {
	APIKeyService       *APIKeyService
	ServiceService      *ServiceService
	DeviceService       *DeviceService
	NotificationService *NotificationService
	WidgetService       *WidgetService
	WidgetConfigService *WidgetConfigService
	FeatureFlagService  *FeatureFlagService
	FingerprintService  *FingerprintService
	FeedbackService     *FeedbackService
	JWTService          *JWTService
}

// NewServiceContainer creates and initializes all services
func NewServiceContainer(db *pgxpool.Pool, dbFing *pgxpool.Pool, cfg *config.Config, logger *zap.Logger) *ServiceContainer {
	// Initialize repositories
	apiKeyRepository := repositories.NewAPIKeyRepository(db)
	serviceRepository := repositories.NewServiceRepository(db)
	widgetRepository := repositories.NewWidgetRepository(db)
	deviceRepository := repositories.NewDeviceRepository(db)
	deviceServiceRepository := repositories.NewDeviceServiceRepository(db)
	deviceNameRepository := repositories.NewDeviceNameRepository(db)
	notificationRepository := repositories.NewNotificationRepository(db)
	notificationDeliveryRepository := repositories.NewNotificationDeliveryRepository(db)
	featureFlagRepository := repositories.NewFeatureFlagRepository(db)
	fingerprintRepository := repositories.NewFingerprintRepository(dbFing, time.Hour)
	feedbackRepository := repositories.NewFeedbackRepository(db)

	// Initialize services with lifecycle context
	widgetConfigService := NewWidgetConfigService(logger)

	return &ServiceContainer{
		APIKeyService:       NewAPIKeyService(apiKeyRepository, &cfg.Auth, logger),
		ServiceService:      NewServiceService(serviceRepository, widgetRepository, widgetConfigService, logger),
		DeviceService:       NewDeviceService(deviceRepository, deviceServiceRepository, deviceNameRepository, db, logger),
		NotificationService: NewNotificationService(notificationRepository, notificationDeliveryRepository, deviceRepository, db, logger),
		WidgetService:       NewWidgetService(widgetRepository, logger),
		WidgetConfigService: widgetConfigService,
		FeatureFlagService:  NewFeatureFlagService(featureFlagRepository, db, logger),
		FingerprintService:  NewFingerprintService(fingerprintRepository, logger, cfg.Fingerprint.Log),
		FeedbackService:     NewFeedbackService(feedbackRepository, logger),
		JWTService:          NewJWTService(logger),
	}
}
