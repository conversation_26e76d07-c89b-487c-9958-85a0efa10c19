# Setup
@host = http://localhost:8082

### TODO test to list services for dashboard
## @name LIST SERVICES
#GET {{host}}/dashboard/services
#Authorization: Bearer jwt_....
#
#> {%
#    client.test("Response status should be 200", () => {
#        client.assert(response.status === 200, `Response status was: ${response.status}`)
#    })
#%}

###
# @name LIST SERVICES as ios
GET {{host}}/mobile/services
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Device-Id: dvc_00000000000000000000000000
X-App-Platform: ios

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should be an array with exactly one service", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length === 1, `Expected 1 service, got: ${response.body.length}`)
    })

    client.test("Service should have correct structure and static values", () => {
        const service = response.body[0]

        // Static fields that should match exactly
        client.assert(service.id === "svc_00000000000000000000000000", `Service ID was: ${service.id}`)
        client.assert(service.logo_url === "https://cdn.appio.so/app/appio.so/logo.png", `Logo URL was: ${service.logo_url}`)
        client.assert(service.banner_url === "https://cdn.appio.so/app/appio.so/banner.jpg", `Banner URL was: ${service.banner_url}`)
        client.assert(service.url === "https://appio.so", `URL was: ${service.url}`)
        client.assert(service.text_color === "#333333", `Text color was: ${service.text_color}`)
        client.assert(service.background_color === "#f0f0f0", `Background color was: ${service.background_color}`)
        client.assert(service.accent_color === "#00aaff", `Accent color was: ${service.accent_color}`)

        // Dynamic fields that should have specific patterns
        client.assert(service.title && service.title.startsWith("new title."), `Title was: ${service.title}`)
        client.assert(service.description && service.description.startsWith("new description."), `Description was: ${service.description}`)

        // Widgets array should exist and have one widget
        client.assert(Array.isArray(service.widgets), `Widgets should be array, was: ${typeof service.widgets}`)
        client.assert(service.widgets.length === 1, `Expected 1 widget, got: ${service.widgets.length}`)
    })

    client.test("Widget should have correct structure and exact config", () => {
        const widget = response.body[0].widgets[0]

        client.assert(widget.id === "wgt_00000000000000000000000000", `Widget ID was: ${widget.id}`)
        client.assert(widget.service_id === "svc_00000000000000000000000000", `Widget service ID was: ${widget.service_id}`)
        client.assert(widget.name === "Ring", `Widget name was: ${widget.name}`)
        client.assert(widget.config && typeof widget.config === "string", `Widget config should be string, was: ${typeof widget.config}`)

        // Expected iOS widget config
        const expectedConfig = "\n{\n\t\"variants\": [\n\t\t{\n\t\t\t\"properties\": {\n\t\t\t\t\"version\": \"1.0\",\n\t\t\t\t\"supportedFamilies\": [\"systemSmall\", \"systemMedium\", \"systemLarge\", \"systemExtraLarge\"],\n\t\t\t\t\"background\": \"clear\"\n\t\t\t},\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"hstack\",\n\t\t\t\t\t\"properties\": {},\n\t\t\t\t\t\"elements\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"spacer\",\n\t\t\t\t\t\t\t\"properties\": {}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"lastUpdated\",\n\t\t\t\t\t\t\t\"properties\": {\n\t\t\t\t\t\t\t\t\"color\": \"secondary\",\n\t\t\t\t\t\t\t\t\"fontSize\": 12,\n\t\t\t\t\t\t\t\t\"padding\": {\n\t\t\t\t\t\t\t\t\t\"right\": 10\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"gauge\",\n\t\t\t\t\t\"properties\": {\n\t\t\t\t\t\t\"value\": 10,\n\t\t\t\t\t\t\"currentValueLabel\": \"10\",\n\t\t\t\t\t\t\"style\": \"accessoryCircularCapacity\",\n\t\t\t\t\t\t\"color\": \"primary\",\n\t\t\t\t\t\t\"tint\": \"primary\",\n\t\t\t\t\t\t\"padding\": {\n\t\t\t\t\t\t\t\"top\": 10,\n\t\t\t\t\t\t\t\"bottom\": 10\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"refreshButton\",\n\t\t\t\t\t\"properties\": {\n\t\t\t\t\t\t\"text\": \"Refresh\",\n\t\t\t\t\t\t\"style\": \"borderedProminent\",\n\t\t\t\t\t\t\"color\": \"#fff\",\n\t\t\t\t\t\t\"padding\": {\n\t\t\t\t\t\t\t\"bottom\": 10\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}\n"
        client.assert(widget.config === expectedConfig, `Widget config was: ${widget.config}`)
    })
%}

###
# @name LIST SERVICES as android
GET {{host}}/mobile/services
Authorization: Bearer dev_44444444444444444444444444444444444444444444444444
X-Device-Id: dvc_00000000000000000000000000
X-App-Platform: android

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should be an array with exactly one service", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length === 1, `Expected 1 service, got: ${response.body.length}`)
    })

    client.test("Service should have correct structure and static values", () => {
        const service = response.body[0]

        // Static fields that should match exactly
        client.assert(service.id === "svc_00000000000000000000000000", `Service ID was: ${service.id}`)
        client.assert(service.logo_url === "https://cdn.appio.so/app/appio.so/logo.png", `Logo URL was: ${service.logo_url}`)
        client.assert(service.banner_url === "https://cdn.appio.so/app/appio.so/banner.jpg", `Banner URL was: ${service.banner_url}`)
        client.assert(service.url === "https://appio.so", `URL was: ${service.url}`)
        client.assert(service.text_color === "#333333", `Text color was: ${service.text_color}`)
        client.assert(service.background_color === "#f0f0f0", `Background color was: ${service.background_color}`)
        client.assert(service.accent_color === "#00aaff", `Accent color was: ${service.accent_color}`)

        // Dynamic fields that should have specific patterns
        client.assert(service.title && service.title.startsWith("new title."), `Title was: ${service.title}`)
        client.assert(service.description && service.description.startsWith("new description."), `Description was: ${service.description}`)

        // Widgets array should exist and have one widget
        client.assert(Array.isArray(service.widgets), `Widgets should be array, was: ${typeof service.widgets}`)
        client.assert(service.widgets.length === 1, `Expected 1 widget, got: ${service.widgets.length}`)
    })

    client.test("Widget should have correct structure and exact config for Android", () => {
        const widget = response.body[0].widgets[0]

        client.assert(widget.id === "wgt_00000000000000000000000000", `Widget ID was: ${widget.id}`)
        client.assert(widget.service_id === "svc_00000000000000000000000000", `Widget service ID was: ${widget.service_id}`)
        client.assert(widget.name === "Ring", `Widget name was: ${widget.name}`)
        client.assert(widget.config && typeof widget.config === "string", `Widget config should be string, was: ${typeof widget.config}`)

        // Expected Android widget config
        const expectedConfig = "\n{\n\t\"variants\": [\n\t\t{\n\t\t\t\"properties\": {\n\t\t\t\t\"version\": \"1.0\",\n\t\t\t\t\"width\": 250,\n\t\t\t\t\"height\": 250,\n\t\t\t\t\"background\": \"clear\"\n\t\t\t},\n\t\t\t\"elements\": [\n                {\n                    \"type\": \"column\",\n                    \"properties\": {\n                        \"height\": \"max\",\n                        \"width\": \"max\",\n                        \"horizontalAlignment\": \"center\",\n                        \"verticalAlignment\": \"center\"\n                    },\n                    \"elements\": [\n                        {\n                            \"type\": \"lastUpdated\",\n                            \"properties\": {\n                                \"width\": \"max\",\n                                \"alignment\": \"end\",\n                                \"color\": \"secondary\",\n                                \"fontSize\": 10\n                            }\n                        },\n                        {\n                            \"type\": \"gauge\",\n                            \"properties\": {\n                                \"value\": 10,\n                                \"label\": \"10\",\n                                \"color\": \"primary\",\n                                \"tint\": \"primary\",\n                                \"fontSize\": 20,\n                                \"size\": 50,\n                                \"padding\": {\n                                    \"top\": 10,\n                                    \"bottom\": 10\n                                }\n                            }\n                        },\n                        {\n                            \"type\": \"refreshButton\"\n                        }\n                    ]\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}\n"
        client.assert(widget.config === expectedConfig, `Widget config was: ${widget.config}`)
    })
%}

###
# @name CREATE SERVICE as demo.appio.so
POST {{host}}/demo-appio-so/services
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111

{
  "title": "Demo title",
  "description": "Demo description",
  "banner_url": "https://cdn.appio.so/app/appio.so/banner.jpg",
  "logo_url": "https://cdn.appio.so/app/appio.so/logo.png",
  "text_color": "#FFFFFF",
  "background_color": "#007aFf",
  "accent_color": "#ff0000"
}

> {%
    client.global.set("demo_service_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain service ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('demo_svc_'), `Response body was: ${JSON.stringify(response.body)}`)
    })
%}

### TODO: GET /services

###
# @name GET SERVICE
GET {{host}}/v1/services/svc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000
# TODO: duplicate service_id ?

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact service data", () => {
        const service = response.body

        // Static fields that should match exactly
        client.assert(service.id === "svc_00000000000000000000000000", `Service ID was: ${service.id}`)
        client.assert(service.logo_url === "https://cdn.appio.so/app/appio.so/logo.png", `Logo URL was: ${service.logo_url}`)
        client.assert(service.banner_url === "https://cdn.appio.so/app/appio.so/banner.jpg", `Banner URL was: ${service.banner_url}`)
        client.assert(service.url === "https://appio.so", `URL was: ${service.url}`)
        client.assert(service.text_color === "#333333", `Text color was: ${service.text_color}`)
        client.assert(service.background_color === "#f0f0f0", `Background color was: ${service.background_color}`)
        client.assert(service.accent_color === "#00aaff", `Accent color was: ${service.accent_color}`)

        // Dynamic fields that should have specific patterns
        client.assert(service.title && service.title.startsWith("new title."), `Title was: ${service.title}`)
        client.assert(service.description && service.description.startsWith("new description."), `Description was: ${service.description}`)
    })
%}

###
# @name GET SERVICE - service does not match API key
GET {{host}}/v1/services/svc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000xxxxxxxxxxxxxxxxxx
# TODO: duplicate service_id ?

> {%
    client.test("Response status should be 400", () => {
        client.assert(response.status === 400, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Invalid input data", `Error message was: ${response.body.error?.message}`)
    })
%}


###
# @name GET SERVICE as app.appio.so
GET {{host}}/app-appio-so/services/{{demo_service_id}}
Authorization: Bearer dev_33333333333333333333333333333333333333333333333333
X-Service-Id: {{demo_service_id}}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain service data", () => {
        client.assert(response.body.id && response.body.id.startsWith('demo_svc_'), `Service ID was: ${response.body.id}`)
        client.assert(response.body.title === "Demo title", `Service title was: ${response.body.title}`)
    })
%}

###
# @name GET SERVICE as app.appio.so - not found
GET {{host}}/app-appio-so/services/svc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_33333333333333333333333333333333333333333333333333
X-Service-Id: svc_00000000xxxxxxxxxxxxxxxxxx

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name GET SERVICE as demo.appio.so
GET {{host}}/demo-appio-so/services/{{demo_service_id}}
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: {{demo_service_id}}

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    if (response.status === 200) {
        client.test("Response body should contain service data", () => {
            client.assert(response.body.id && response.body.id.startsWith('demo_svc_'), `Service ID was: ${response.body.id}`)
            client.assert(response.body.title === "Demo title", `Service title was: ${response.body.title}`)
        })
    } else if (response.status === 404) {
        client.test("Response body should contain exact error message", () => {
            client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
        })
    }
%}

###
# @name GET SERVICE as demo.appio.so
GET {{host}}/demo-appio-so/services/demo_svc_01jrw63pyet5d1htndrc7vva2w
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_01jrw63pyet5d1htndrc7vva2w

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    if (response.status === 200) {
        client.test("Response body should contain service data", () => {
            client.assert(response.body.id && response.body.id.startsWith('demo_svc_'), `Service ID was: ${response.body.id}`)
            client.assert(response.body.title === "Demo title", `Service title was: ${response.body.title}`)
        })
    } else if (response.status === 404) {
        client.test("Response body should contain exact error message", () => {
            client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
        })
    }
%}

###
# @name GET SERVICE as ios
GET {{host}}/mobile/services/{{demo_service_id}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: {{demo_service_id}}
X-App-Platform: ios
#X-Device-Id: dvc_00000000000000000000000000   # not needed, but client might send it if it has it

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    if (response.status === 200) {
        client.test("Response body should contain service data", () => {
            client.assert(response.body.id && response.body.id.startsWith('demo_svc_'), `Service ID was: ${response.body.id}`)
            client.assert(response.body.title === "Demo title", `Service title was: ${response.body.title}`)
        })
    } else if (response.status === 404) {
        client.test("Response body should contain exact error message", () => {
            client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
        })
    }
%}

###
# @name UPDATE SERVICE
PATCH {{host}}/v1/services/svc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000
# TODO: duplicate service_id?

{
  "title": "new title. {{$uuid}}",
  "description": "new description. {{$isoTimestamp}}",
  "text_color": "#333333",
  "background_color": "#F0F0F0",
  "accent_color": "#00aaff"
}

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    if (response.status === 200) {
        client.test("Response body should contain service ID", () => {
            client.assert(response.body.id && response.body.id.startsWith('svc_'), `Service ID was: ${response.body.id}`)
        })
    } else if (response.status === 404) {
        client.test("Response body should contain exact error message", () => {
            client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
        })
    }
%}

###
# @name DELETE SERVICE
# n/a

###