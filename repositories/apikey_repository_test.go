package repositories

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewAPIKeyRepository(t *testing.T) {
	t.Run("Creates repository correctly", func(t *testing.T) {
		repo := NewAPIKeyRepository(nil)
		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB)
	})

	t.Run("Creates repository with non-nil DB", func(t *testing.T) {
		// In a real scenario, you'd pass a real DB connection
		// For this test, we're just validating the constructor works
		repo := NewAPIKeyRepository(nil)
		assert.NotNil(t, repo)
		assert.Equal(t, (*APIKeyRepository)(repo), repo)
	})
}
