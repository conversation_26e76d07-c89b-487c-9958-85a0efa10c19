package repositories

import (
	"context"
	"fmt"

	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
)

type APIKeyRepositoryInterface interface {
	GetServiceIDByAPIKey(ctx context.Context, apiKey string) (*appioid.ID, error)
}

type APIKeyRepository struct {
	DB *pgxpool.Pool
}

func NewAPIKeyRepository(db *pgxpool.Pool) *APIKeyRepository {
	return &APIKeyRepository{
		DB: db,
	}
}

func (r *APIKeyRepository) GetServiceIDByAPIKey(ctx context.Context, apiKey string) (*appioid.ID, error) {
	var pgServiceId pgtype.Text
	query := "SELECT service_id FROM api_keys WHERE api_key=@api_key AND deactivated_at IS NULL"
	args := pgx.NamedArgs{"api_key": apiKey}
	err := r.DB.QueryRow(ctx, query, args).Scan(&pgServiceId)
	if err != nil {
		return nil, fmt.Errorf("selecting serviceID and role from api keys: %w", err)
	}

	if !pgServiceId.Valid {
		return nil, nil
	}

	svcID, err := appioid.Parse(pgServiceId.String)
	if err != nil {
		return nil, fmt.Errorf("parsing service ID: %w", err)
	}
	return svcID, nil
}
